<template>
  <div class="home">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background">
        <div class="hero-shapes">
          <div class="shape shape-1"></div>
          <div class="shape shape-2"></div>
          <div class="shape shape-3"></div>
        </div>
      </div>

      <div class="hero-container">
        <div class="hero-content">
          <div class="hero-badge">
            <el-icon><Trophy /></el-icon>
            <span>{{ $t('home.hero.badge') }}</span>
          </div>

          <h1 class="hero-title">
            <span class="title-main">{{ $t('home.hero.titleMain') }}</span>
            <span class="title-highlight">{{ $t('home.hero.titleHighlight') }}</span>
          </h1>

          <p class="hero-subtitle">{{ $t('home.hero.subtitle') }}</p>

          <div class="hero-actions">
            <el-button
              type="primary"
              size="large"
              class="hero-btn-primary"
              @click="handlePrimaryAction"
            >
              <el-icon><CaretRight /></el-icon>
              {{ isAuthenticated ? $t('home.hero.startPractice') : $t('home.hero.getStarted') }}
            </el-button>

            <el-button
              size="large"
              class="hero-btn-secondary"
              @click="handleSecondaryAction"
            >
              <el-icon><VideoPlay /></el-icon>
              {{ $t('home.hero.watchDemo') }}
            </el-button>
          </div>

          <div class="hero-stats">
            <div class="stat-item" v-for="stat in heroStats" :key="stat.key">
              <div class="stat-number">{{ stat.value }}</div>
              <div class="stat-label">{{ $t(stat.label) }}</div>
            </div>
          </div>
        </div>

        <div class="hero-visual">
          <div class="hero-image-container">
            <div class="floating-cards">
              <div class="card card-1">
                <el-icon><Document /></el-icon>
                <span>{{ $t('home.hero.card1') }}</span>
              </div>
              <div class="card card-2">
                <el-icon><TrendCharts /></el-icon>
                <span>{{ $t('home.hero.card2') }}</span>
              </div>
              <div class="card card-3">
                <el-icon><Star /></el-icon>
                <span>{{ $t('home.hero.card3') }}</span>
              </div>
            </div>
            <div class="hero-main-visual">
              <div class="dashboard-mockup">
                <div class="mockup-header">
                  <div class="mockup-dots">
                    <span></span><span></span><span></span>
                  </div>
                </div>
                <div class="mockup-content">
                  <div class="progress-ring">
                    <svg viewBox="0 0 100 100">
                      <circle cx="50" cy="50" r="45" fill="none" stroke="#e6f4ff" stroke-width="8"/>
                      <circle cx="50" cy="50" r="45" fill="none" stroke="#1890ff" stroke-width="8"
                              stroke-dasharray="283" stroke-dashoffset="85" stroke-linecap="round"/>
                    </svg>
                    <div class="progress-text">
                      <div class="progress-number">85%</div>
                      <div class="progress-label">{{ $t('home.hero.progress') }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features">
      <div class="section-container">
        <div class="section-header">
          <h2 class="section-title">{{ $t('home.features.title') }}</h2>
          <p class="section-subtitle">{{ $t('home.features.subtitle') }}</p>
        </div>

        <div class="features-grid">
          <div
            class="feature-card"
            v-for="(feature, index) in features"
            :key="feature.key"
            :style="{ '--delay': index * 0.1 + 's' }"
          >
            <div class="feature-icon">
              <el-icon :size="32"><component :is="feature.icon" /></el-icon>
            </div>
            <h3 class="feature-title">{{ $t(feature.title) }}</h3>
            <p class="feature-description">{{ $t(feature.description) }}</p>
            <div class="feature-link">
              <router-link :to="feature.link" class="learn-more">
                {{ $t('home.features.learnMore') }}
                <el-icon><ArrowRight /></el-icon>
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="stats" v-if="statsData.length > 0">
      <div class="section-container">
        <div class="stats-grid">
          <div
            class="stat-card"
            v-for="(stat, index) in statsData"
            :key="stat.key"
            :style="{ '--delay': index * 0.1 + 's' }"
          >
            <div class="stat-icon">
              <el-icon><component :is="stat.icon" /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number" :data-target="stat.value">{{ stat.displayValue }}</div>
              <div class="stat-label">{{ $t(stat.label) }}</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
      <div class="section-container">
        <div class="cta-content">
          <h2 class="cta-title">{{ $t('home.cta.title') }}</h2>
          <p class="cta-subtitle">{{ $t('home.cta.subtitle') }}</p>
          <div class="cta-actions">
            <el-button
              type="primary"
              size="large"
              @click="handleGetStarted"
            >
              {{ $t('home.cta.getStarted') }}
            </el-button>
            <el-button
              size="large"
              @click="handleLearnMore"
            >
              {{ $t('home.cta.learnMore') }}
            </el-button>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="section-container">
        <div class="footer-content">
          <div class="footer-main">
            <div class="footer-brand">
              <div class="brand-logo">
                <el-icon><CaretRight /></el-icon>
              </div>
              <h3 class="brand-name">{{ $t('common.appName') }}</h3>
              <p class="brand-description">{{ $t('home.footer.description') }}</p>
            </div>

            <div class="footer-links">
              <div class="link-group">
                <h4 class="link-title">{{ $t('home.footer.features') }}</h4>
                <ul class="link-list">
                  <li><router-link to="/practice">{{ $t('home.footer.practice') }}</router-link></li>
                  <li><router-link to="/accident-report">{{ $t('home.footer.accident') }}</router-link></li>
                  <li><router-link to="/schools">{{ $t('home.footer.schools') }}</router-link></li>
                </ul>
              </div>

              <div class="link-group">
                <h4 class="link-title">{{ $t('home.footer.support') }}</h4>
                <ul class="link-list">
                  <li><a href="#" @click.prevent="handleHelp">{{ $t('home.footer.help') }}</a></li>
                  <li><a href="#" @click.prevent="handleFAQ">{{ $t('home.footer.faq') }}</a></li>
                  <li><a href="#" @click.prevent="handleContact">{{ $t('home.footer.contact') }}</a></li>
                </ul>
              </div>

              <div class="link-group">
                <h4 class="link-title">{{ $t('home.footer.legal') }}</h4>
                <ul class="link-list">
                  <li><a href="#" @click.prevent="handlePrivacy">{{ $t('home.footer.privacy') }}</a></li>
                  <li><a href="#" @click.prevent="handleTerms">{{ $t('home.footer.terms') }}</a></li>
                </ul>
              </div>
            </div>
          </div>

          <div class="footer-bottom">
            <div class="footer-copyright">
              <p>&copy; 2024 {{ $t('common.appName') }}. {{ $t('home.footer.rights') }}</p>
            </div>
            <div class="footer-language">
              <LanguageSwitcher variant="text" size="small" :show-text="true" />
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import LanguageSwitcher from '@/components/common/LanguageSwitcher.vue'
import {
  Document,
  TrendCharts,
  Star,
  Warning,
  Trophy,
  CaretRight,
  VideoPlay,
  ArrowRight,
  DataBoard,
  User,
  CheckBadge
} from '@element-plus/icons-vue'

export default {
  name: 'Home',
  components: {
    LanguageSwitcher,
    Document,
    TrendCharts,
    Star,
    Warning,
    Trophy,
    CaretRight,
    VideoPlay,
    ArrowRight,
    DataBoard,
    User,
    CheckBadge
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    const { t } = useI18n()

    // 响应式数据
    const statsData = ref([])
    const loading = ref(false)

    // 计算属性
    const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])

    // Hero区域统计数据
    const heroStats = computed(() => [
      { key: 'questions', value: '10K+', label: 'home.hero.stats.questions' },
      { key: 'users', value: '5K+', label: 'home.hero.stats.users' },
      { key: 'success', value: '95%', label: 'home.hero.stats.success' }
    ])

    // 功能特色数据
    const features = computed(() => [
      {
        key: 'practice',
        icon: 'Document',
        title: 'home.features.practice.title',
        description: 'home.features.practice.description',
        link: '/practice'
      },
      {
        key: 'progress',
        icon: 'TrendCharts',
        title: 'home.features.progress.title',
        description: 'home.features.progress.description',
        link: '/dashboard'
      },
      {
        key: 'bookmarks',
        icon: 'Star',
        title: 'home.features.bookmarks.title',
        description: 'home.features.bookmarks.description',
        link: '/bookmarks'
      },
      {
        key: 'accident',
        icon: 'Warning',
        title: 'home.features.accident.title',
        description: 'home.features.accident.description',
        link: '/accident-report'
      }
    ])

    // 方法
    const handlePrimaryAction = () => {
      if (isAuthenticated.value) {
        router.push('/practice')
      } else {
        router.push('/register')
      }
    }

    const handleSecondaryAction = () => {
      // 播放演示视频或跳转到介绍页面
      ElMessage.info(t('home.messages.demoComingSoon'))
    }

    const handleGetStarted = () => {
      if (isAuthenticated.value) {
        router.push('/dashboard')
      } else {
        router.push('/register')
      }
    }

    const handleLearnMore = () => {
      // 滚动到功能介绍区域
      const featuresSection = document.querySelector('.features')
      if (featuresSection) {
        featuresSection.scrollIntoView({ behavior: 'smooth' })
      }
    }

    // 页脚链接处理
    const handleHelp = () => {
      ElMessage.info(t('home.messages.helpComingSoon'))
    }

    const handleFAQ = () => {
      ElMessage.info(t('home.messages.faqComingSoon'))
    }

    const handleContact = () => {
      ElMessage.info(t('home.messages.contactComingSoon'))
    }

    const handlePrivacy = () => {
      ElMessage.info(t('home.messages.privacyComingSoon'))
    }

    const handleTerms = () => {
      ElMessage.info(t('home.messages.termsComingSoon'))
    }

    // 获取统计数据
    const fetchStats = async () => {
      try {
        loading.value = true
        // TODO: 从API获取真实统计数据
        // const response = await api.getStats()

        // 模拟数据，后续替换为真实API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        statsData.value = [
          {
            key: 'totalQuestions',
            icon: 'Document',
            value: 10000,
            displayValue: '10,000+',
            label: 'home.stats.totalQuestions'
          },
          {
            key: 'activeUsers',
            icon: 'User',
            value: 5000,
            displayValue: '5,000+',
            label: 'home.stats.activeUsers'
          },
          {
            key: 'successRate',
            icon: 'CheckBadge',
            value: 95,
            displayValue: '95%',
            label: 'home.stats.successRate'
          },
          {
            key: 'practiceCount',
            icon: 'DataBoard',
            value: 50000,
            displayValue: '50K+',
            label: 'home.stats.practiceCount'
          }
        ]
      } catch (error) {
        console.error('Failed to fetch stats:', error)
        // 使用默认数据
        statsData.value = []
      } finally {
        loading.value = false
      }
    }

    // 生命周期
    onMounted(() => {
      fetchStats()
    })

    return {
      isAuthenticated,
      heroStats,
      features,
      statsData,
      loading,
      handlePrimaryAction,
      handleSecondaryAction,
      handleGetStarted,
      handleLearnMore,
      handleHelp,
      handleFAQ,
      handleContact,
      handlePrivacy,
      handleTerms
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
  overflow-x: hidden;
}

// 通用容器
.section-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;

  @media (max-width: 768px) {
    padding: 0 1rem;
  }
}

// Hero Section
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  color: white;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-shapes {
  position: absolute;
  width: 100%;
  height: 100%;

  .shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;

    &.shape-1 {
      width: 300px;
      height: 300px;
      top: 10%;
      right: 10%;
      animation-delay: 0s;
    }

    &.shape-2 {
      width: 200px;
      height: 200px;
      bottom: 20%;
      left: 5%;
      animation-delay: 2s;
    }

    &.shape-3 {
      width: 150px;
      height: 150px;
      top: 50%;
      left: 80%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-container {
  position: relative;
  z-index: 2;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }

  @media (max-width: 768px) {
    padding: 0 1rem;
    gap: 2rem;
  }
}

.hero-content {
  animation: slideInLeft 1s ease-out;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 0.5rem 1rem;
  border-radius: 2rem;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;

  .title-main {
    display: block;
  }

  .title-highlight {
    display: block;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  margin-bottom: 2.5rem;
  opacity: 0.9;
  max-width: 500px;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 3rem;

  @media (max-width: 640px) {
    flex-direction: column;
    align-items: center;
  }
}

.hero-btn-primary {
  background: linear-gradient(45deg, #1890ff, #36cfc9);
  border: none;
  box-shadow: 0 4px 15px rgba(24, 144, 255, 0.4);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(24, 144, 255, 0.6);
  }
}

.hero-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }
}

.hero-stats {
  display: flex;
  gap: 2rem;

  @media (max-width: 640px) {
    justify-content: center;
    gap: 1.5rem;
  }

  .stat-item {
    text-align: center;

    .stat-number {
      font-size: 1.5rem;
      font-weight: 700;
      margin-bottom: 0.25rem;
    }

    .stat-label {
      font-size: 0.875rem;
      opacity: 0.8;
    }
  }
}

.hero-visual {
  animation: slideInRight 1s ease-out;

  @media (max-width: 1024px) {
    order: -1;
  }
}

.hero-image-container {
  position: relative;
  height: 500px;

  @media (max-width: 768px) {
    height: 300px;
  }
}

.floating-cards {
  position: absolute;
  width: 100%;
  height: 100%;

  .card {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--el-text-color-primary);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    animation: cardFloat 4s ease-in-out infinite;

    .el-icon {
      color: var(--el-color-primary);
      font-size: 1.5rem;
    }

    span {
      font-weight: 500;
      font-size: 0.875rem;
    }

    &.card-1 {
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    &.card-2 {
      top: 50%;
      right: 10%;
      animation-delay: 1.5s;
    }

    &.card-3 {
      bottom: 20%;
      left: 20%;
      animation-delay: 3s;
    }
  }
}

@keyframes cardFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.hero-main-visual {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 300px;

  @media (max-width: 768px) {
    width: 200px;
    height: 200px;
  }
}

.dashboard-mockup {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.mockup-header {
  height: 40px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  padding: 0 1rem;
  border-bottom: 1px solid #e0e0e0;
}

.mockup-dots {
  display: flex;
  gap: 0.5rem;

  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;

    &:nth-child(1) { background: #ff5f57; }
    &:nth-child(2) { background: #ffbd2e; }
    &:nth-child(3) { background: #28ca42; }
  }
}

.mockup-content {
  padding: 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100% - 40px);
}

.progress-ring {
  position: relative;
  width: 120px;
  height: 120px;

  svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
  }

  circle {
    transition: stroke-dashoffset 0.5s ease;
  }
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: var(--el-text-color-primary);

  .progress-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--el-color-primary);
  }

  .progress-label {
    font-size: 0.75rem;
    margin-top: 0.25rem;
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// Features Section
.features {
  padding: 6rem 0;
  background: linear-gradient(180deg, #f8fafc 0%, #ffffff 100%);
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;

  .section-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--el-text-color-primary);
    margin-bottom: 1rem;
  }

  .section-subtitle {
    font-size: 1.125rem;
    color: var(--el-text-color-regular);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.feature-card {
  background: white;
  border-radius: 1.5rem;
  padding: 2.5rem 2rem;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  animation: fadeInUp 0.6s ease-out forwards;
  animation-delay: var(--delay);
  opacity: 0;
  transform: translateY(30px);

  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--el-color-primary-light-7);
  }
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--el-color-primary-light-8), var(--el-color-primary-light-9));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  color: var(--el-color-primary);
  transition: all 0.3s ease;

  .el-icon {
    font-size: 2rem;
  }

  .feature-card:hover & {
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-dark-2));
    color: white;
    transform: scale(1.1);
  }
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--el-text-color-regular);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.feature-link {
  .learn-more {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--el-color-primary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      color: var(--el-color-primary-dark-2);
      transform: translateX(4px);
    }

    .el-icon {
      font-size: 0.875rem;
      transition: transform 0.3s ease;
    }

    &:hover .el-icon {
      transform: translateX(4px);
    }
  }
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Stats Section
.stats {
  padding: 4rem 0;
  background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-dark-2));
  color: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }
}

.stats-grid {
  position: relative;
  z-index: 1;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

.stat-card {
  text-align: center;
  padding: 1.5rem;
  border-radius: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  animation: fadeInUp 0.6s ease-out forwards;
  animation-delay: var(--delay);
  opacity: 0;
  transform: translateY(30px);

  &:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.15);
  }
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  color: white;

  .el-icon {
    font-size: 1.5rem;
  }
}

.stat-content {
  .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    background: linear-gradient(45deg, #ffffff, #f0f9ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .stat-label {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 500;
  }
}

// CTA Section
.cta {
  padding: 6rem 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  text-align: center;
}

.cta-content {
  max-width: 600px;
  margin: 0 auto;

  .cta-title {
    font-size: clamp(2rem, 4vw, 2.5rem);
    font-weight: 700;
    color: var(--el-text-color-primary);
    margin-bottom: 1rem;
  }

  .cta-subtitle {
    font-size: 1.125rem;
    color: var(--el-text-color-regular);
    margin-bottom: 2.5rem;
    line-height: 1.6;
  }
}

.cta-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;

  @media (max-width: 640px) {
    flex-direction: column;
    align-items: center;
  }
}

// Footer
.footer {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  }
}

.footer-content {
  position: relative;
  z-index: 1;
  padding: 4rem 0 2rem;
}

.footer-main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 4rem;
  margin-bottom: 3rem;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 3rem;
    text-align: center;
  }
}

.footer-brand {
  .brand-logo {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--el-color-primary), var(--el-color-primary-dark-2));
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: white;
    font-size: 1.5rem;

    @media (max-width: 1024px) {
      margin: 0 auto 1.5rem;
    }
  }

  .brand-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: white;
  }

  .brand-description {
    color: #cbd5e1;
    line-height: 1.6;
    max-width: 300px;

    @media (max-width: 1024px) {
      margin: 0 auto;
    }
  }
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
  }
}

.link-group {
  .link-title {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: white;
  }

  .link-list {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      margin-bottom: 0.75rem;

      a {
        color: #cbd5e1;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;

        &:hover {
          color: white;
          transform: translateX(4px);
        }
      }
    }
  }
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
}

.footer-copyright {
  color: #94a3b8;
  font-size: 0.875rem;
}

.footer-language {
  :deep(.language-switcher) {
    .el-button {
      background: rgba(255, 255, 255, 0.1);
      border-color: rgba(255, 255, 255, 0.2);
      color: white;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .hero-container {
    padding: 2rem 1rem;
  }

  .section-container {
    padding: 0 1.5rem;
  }
}

@media (max-width: 768px) {
  .hero {
    min-height: 80vh;
    padding: 2rem 0;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.125rem;
  }

  .features {
    padding: 4rem 0;
  }

  .stats {
    padding: 3rem 0;
  }

  .cta {
    padding: 4rem 0;
  }
}

@media (max-width: 480px) {
  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .hero-actions {
    width: 100%;

    .el-button {
      width: 100%;
      max-width: 300px;
    }
  }

  .features-grid {
    gap: 1rem;
  }

  .feature-card {
    padding: 2rem 1.5rem;
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .features {
    background: linear-gradient(180deg, #0f172a 0%, #1e293b 100%);
  }

  .feature-card {
    background: #1e293b;
    border-color: #334155;
    color: #f1f5f9;
  }

  .cta {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }
}

// 打印样式
@media print {
  .hero-shapes,
  .floating-cards,
  .hero-visual {
    display: none;
  }

  .hero {
    background: white;
    color: black;
    min-height: auto;
    padding: 2rem 0;
  }

  .stats,
  .cta,
  .footer {
    display: none;
  }
}
</style>
