/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AnimatedCounter: typeof import('./src/components/home/<USER>')['default']
    AppBottomNav: typeof import('./src/components/layout/AppBottomNav.vue')['default']
    AppFooter: typeof import('./src/components/layout/AppFooter.vue')['default']
    AppLayout: typeof import('./src/components/layout/AppLayout.vue')['default']
    AppNavbar: typeof import('./src/components/layout/AppNavbar.vue')['default']
    AppSidebar: typeof import('./src/components/layout/AppSidebar.vue')['default']
    CTASection: typeof import('./src/components/home/<USER>')['default']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRate: typeof import('element-plus/es')['ElRate']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElTag: typeof import('element-plus/es')['ElTag']
    FeatureCard: typeof import('./src/components/common/FeatureCard.vue')['default']
    FeaturesSection: typeof import('./src/components/home/<USER>')['default']
    GradientCard: typeof import('./src/components/common/GradientCard.vue')['default']
    HeroSection: typeof import('./src/components/home/<USER>')['default']
    LanguageSwitcher: typeof import('./src/components/common/LanguageSwitcher.vue')['default']
    QuickNav: typeof import('./src/components/layout/QuickNav.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StatsSection: typeof import('./src/components/home/<USER>')['default']
    TestimonialsSection: typeof import('./src/components/home/<USER>')['default']
  }
}
