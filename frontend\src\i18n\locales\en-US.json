{"common": {"appName": "DriveEasy Pass", "login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "submit": "Submit", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "search": "Search", "loading": "Loading...", "noData": "No Data", "success": "Success", "error": "Error", "warning": "Warning", "info": "Info", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "refresh": "Refresh", "reset": "Reset", "clear": "Clear", "select": "Select", "upload": "Upload", "download": "Download", "export": "Export", "import": "Import", "view": "View", "add": "Add", "update": "Update", "remove": "Remove", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "settings": "Settings", "help": "Help", "about": "About", "language": "Language", "theme": "Theme", "profile": "Profile", "notifications": "Notifications", "home": "Home", "pleaseEnter": "Please enter", "pleaseSelect": "Please select", "unknown": "Unknown", "notSet": "Not set", "exportData": "Export Data", "clearFilters": "Clear Filters", "backToHome": "Back to Home", "goBack": "Go Back", "enabled": "Enabled", "disabled": "Disabled", "normal": "Normal", "abnormal": "Abnormal", "failed": "Failed", "pending": "Pending", "completed": "Completed", "confirmModify": "Confirm Modify", "confirmDelete": "Confirm Delete", "finalConfirm": "Final Confirmation", "allquestionname": "QUESTIONS", "allQuestions": "All Questions", "allQuestionsDesc": "Selected questions for driving test - No category"}, "home": {"hero": {"badge": "🏆 Professional Driving Platform", "titleMain": "Smart Driving Practice", "titleHighlight": "Pass with Confidence", "subtitle": "AI-powered personalized learning system with comprehensive question banks for subjects 1 & 4. Study efficiently and pass on your first try!", "getStarted": "Get Started", "startPractice": "Start Practice", "watchDemo": "Watch Demo", "progress": "Learning Progress", "card1": "Smart Questions", "card2": "Progress Track", "card3": "Review Favorites", "stats": {"questions": "Questions", "users": "Active Users", "success": "Success Rate"}}, "features": {"title": "Core Features", "subtitle": "Comprehensive driving test learning solutions designed to make your preparation more efficient and intelligent", "learnMore": "Learn More", "practice": {"title": "Smart Question Bank", "description": "Massive curated question bank with AI-powered recommendations and personalized learning paths for maximum efficiency"}, "progress": {"title": "Progress Tracking", "description": "Real-time learning progress tracking with data visualization and scientific review planning"}, "bookmarks": {"title": "Wrong Question Collection", "description": "One-click bookmark for difficult questions with smart review reminders to strengthen weak points"}, "accident": {"title": "Accident Case Studies", "description": "Real accident case analysis to enhance safe driving awareness and emergency response skills"}}, "stats": {"totalQuestions": "Total Questions", "activeUsers": "Active Users", "successRate": "Success Rate", "practiceCount": "Practice Sessions"}, "cta": {"title": "Ready to Start Your Driving Test Journey?", "subtitle": "Join us and experience the most professional driving test learning platform. Make passing your test easier!", "getStarted": "Get Started", "learnMore": "Learn More"}, "footer": {"description": "Professional driving test practice platform to help you pass your driving test with ease", "features": "Features", "practice": "Question Practice", "accident": "Accident Records", "schools": "Driving Schools", "support": "Support", "help": "Help Center", "faq": "FAQ", "contact": "Contact Us", "legal": "Legal", "privacy": "Privacy Policy", "terms": "Terms of Service", "rights": "All rights reserved"}, "messages": {"demoComingSoon": "Demo feature coming soon", "helpComingSoon": "Help documentation is being improved", "faqComingSoon": "FAQ page coming soon", "contactComingSoon": "Contact information will be announced soon", "privacyComingSoon": "Privacy policy will be released soon", "termsComingSoon": "Terms of service will be released soon"}}, "navigation": {"mobileMenu": "Mobile Menu", "userCenter": "User Center", "profile": "Profile", "bookmarks": "Bookmarks", "logout": "Logout", "logoutFailed": "Logout failed", "logoutConfirm": "Are you sure you want to logout?", "logoutSuccess": "Logout successful", "adminRole": "Administrator", "userRole": "Regular User", "adminPanel": "Admin Panel", "mainFeatures": "Main Features", "dashboard": "Dashboard", "practice": "Practice", "accident": "Accident Report", "admin": {"dashboard": "Admin Overview", "questions": "Question Management", "users": "User Management", "reports": "Data Reports"}}, "auth": {"loginTitle": "User Login", "logindes": "DriveEasy Pass", "registerTitle": "User Registration", "username": "Username", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "noAccount": "Don't have an account?", "hasAccount": "Already have an account?", "loginSuccess": "Login successful", "logoutSuccess": "Logout successful", "registerSuccess": "Registration successful! Welcome to DriveEasy Pass", "confirmLogout": "Are you sure you want to logout?", "loginFailed": "<PERSON><PERSON> failed", "registerFailed": "Registration failed", "invalidCredentials": "Invalid username or password", "userExists": "User already exists", "weakPassword": "Password is too weak", "passwordMismatch": "Passwords do not match", "loggingIn": "Logging in...", "login": "<PERSON><PERSON>", "registerNow": "Register Now", "startJourney": "Create your account and start your driving test journey", "practiceDescription": "Professional question bank practice to help you pass the driving test easily", "pleaseAgreeTerms": "Please agree to the terms and privacy policy first", "enterUsername": "Please enter username", "enterPassword": "Please enter password", "enterEmail": "Please enter email", "agreeTo": "I have read and agree to the", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "registering": "Registering...", "registerIllustrationAlt": "Registration Illustration", "joinApp": "Join DriveEasy Pass", "joinDescription": "Join thousands of learners to pass your driving test with ease", "terms": {"section1": {"title": "1. Terms of Service", "content": "By registering and using DriveEasy Pass, you agree to comply with the following terms."}, "section2": {"title": "2. User Responsibilities", "content": "You are responsible for protecting your account information, including your username and password."}, "section3": {"title": "3. Service Content", "content": "We provide driving test practice, accident records, and related services."}, "section4": {"title": "4. Privacy Protection", "content": "We are committed to protecting your personal information. See our Privacy Policy for details."}}, "privacy": {"section1": {"title": "1. Information Collection", "content": "We only collect information necessary to provide our services."}, "section2": {"title": "2. Information Use", "content": "Your information is used solely to improve service quality and user experience."}, "section3": {"title": "3. Information Protection", "content": "We adopt industry-standard security measures to protect your information."}, "section4": {"title": "4. Information Sharing", "content": "We will not share your personal information with third parties without your consent."}}}, "dashboard": {"welcome": "Welcome back", "welcomeMessage": "Welcome back, {username}!", "defaultUser": "User", "subtitle": "Continue your driving test journey, one step closer to getting your license", "todayPractice": "Today's Practice", "totalQuestions": "Total Questions", "correctRate": "Correct Rate", "practiceTime": "Practice Time", "recentActivity": "Recent Activity", "quickActions": "Quick Actions", "quickActionsDesc": "Quick access to common features", "startPractice": "Start Practice", "startPracticeDesc": "Subject 1 & 4 Question Bank", "viewBookmarks": "View Bookmarks", "bookmarkReview": "Bookmark Review", "bookmarkReviewDesc": "Review bookmarked questions", "practiceHistory": "Practice History", "achievements": "Achievements", "accidentReport": "Accident Report", "accidentReportDesc": "Record and manage accidents", "schoolInfo": "School Information", "schoolInfoDesc": "Find nearby driving schools", "recentPractices": "Recent Practices", "viewAll": "View All", "noPracticeRecords": "No practice records", "startFirstPractice": "Start your first practice", "passed": "Passed", "failed": "Failed", "categoryStats": "Category Practice Statistics", "noStatsData": "No statistics data", "averageScoreLabel": "Average Score", "practiceCountLabel": "Practice Count", "passedCountLabel": "Passed Count", "bestScoreLabel": "Best Score", "practiceCountStat": "Practice Count", "totalQuestionsStat": "Total Questions", "averageScoreStat": "Average Score", "bookmarkedQuestionsStat": "Bookmarked Questions", "fetchStatsFailed": "Failed to fetch statistics"}, "practice": {"title": "Practice", "subtitle": "Select a question category to start practicing and improve your driving test scores", "selectCategory": "Select Category", "randomPractice": "Random Practice", "sequentialPractice": "Sequential Practice", "wrongQuestions": "Wrong Questions", "mockExam": "<PERSON><PERSON>", "questionCount": "Question Count", "timeLimit": "Time Limit", "difficulty": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "Category", "startPractice": "Start Practice", "submitAnswer": "Submit Answer", "nextQuestion": "Next Question", "previousQuestion": "Previous Question", "bookmark": "Bookmark", "unbookmark": "Remove Bookmark", "explanation": "Explanation", "correct": "Correct", "incorrect": "Incorrect", "score": "Score", "timeUsed": "Time Used", "accuracy": "Accuracy", "bookmarkedQuestions": "Bookmarked Questions", "practiceRecords": "Practice Records", "noCategoriesAvailable": "No question categories available", "contactAdminToAddCategories": "Please contact administrator to add question categories", "practiceCount": "Practice Count", "averageScore": "Average Score", "bestScore": "Best Score", "passedCount": "Passed Count", "masteryLevel": "Mastery Level", "viewDetails": "View Details", "subcategories": "Subcategories", "practiceCountSuffix": " practices", "bookmarkReview": "Bookmark Review", "reviewBookmarkedQuestions": "Review your important bookmarked questions and consolidate knowledge", "startReview": "Start Review", "practiceSettings": "Practice Settings", "questionCountOptions": {"10": "10 Questions", "20": "20 Questions", "30": "30 Questions", "50": "50 Questions"}, "practiceMode": "Practice Mode", "practiceModeName": "Practice Mode", "examMode": "Exam <PERSON>", "examModeName": "Exam <PERSON>", "showAnswer": "Show Answer", "showImmediately": "Show Immediately", "showAfterSubmit": "Show After Submit", "progress": "Progress", "fetchCategoriesFailed": "Failed to fetch categories", "fetchBookmarkCountFailed": "Failed to fetch bookmark count", "createSessionFailed": "Failed to create practice session", "backToPractice": "Back to Practice", "goPractice": "Go Practice", "removeBookmark": "Remove Bookmark", "hideAnswer": "Hide Answer", "noBookmarks": "No bookmarked questions", "noQuestionsFound": "No related questions found", "tryOtherConditions": "Please try other search conditions", "bookmarkDescription": "Bookmark important questions during practice for later review", "bookmarkTime": "Bookmark Time", "answerAnalysis": "Answer Analysis", "failedTitle": "Failed", "failedSubtitle": "Unfortunately, you did not pass this practice. Please keep trying!", "passedTitle": "Passed", "passedSubtitle": "Congratulations! You passed this practice. Keep it up!", "finalScore": "Final Score", "correctCount": "Correct Answers", "totalCount": "Total Questions", "reviewAnswers": "Review Answers", "restartPractice": "Restart Practice", "backToList": "Back to List", "completePractice": "Complete Practice", "questionNumber": "Question {number}"}, "profile": {"title": "Profile", "basicInfo": "Basic Information", "statistics": "Statistics", "settings": "Settings", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "avatar": "Avatar", "phone": "Phone", "birthday": "Birthday", "gender": "Gender", "male": "Male", "female": "Female", "languagePreference": "Language Preference", "themePreference": "Theme Preference", "light": "Light", "dark": "Dark", "auto": "Auto", "editProfile": "Edit Profile", "manageInfo": "Manage your personal information and account settings", "deleteAccount": "Delete Account", "confirmNewPassword": "Confirm New Password", "confirmPassword": "Confirm Password", "enterCurrentPassword": "Please enter current password", "enterNewPassword": "Please enter new password", "enterConfirmPassword": "Please enter new password again", "confirmDeleteAccount": "Are you sure you want to delete your account? This action is irreversible!", "basicInfoDescription": "Manage your basic personal information", "passwordSecurity": "Password Security", "passwordSecurityDescription": "Change your login password", "accountInfo": "Account Information", "accountInfoDescription": "View your account details", "userId": "User ID:", "accountType": "Account Type:", "registeredAt": "Registered At:", "lastLogin": "Last Login:", "accountStatus": "Account Status:", "updatedAt": "Updated At:", "dataManagement": "Data Management", "dataManagementDescription": "Manage your personal data", "deleteAccountWarning": "This action will permanently delete your account and all related data, including:", "deleteProfileInfo": "Profile information", "deletePracticeRecords": "Practice records and scores", "deleteBookmarkedQuestions": "Bookmarked questions", "deleteAccidentRecords": "Accident records", "deleteIrreversible": "This action is irreversible!"}, "admin": {"title": "Admin Panel", "subtitle": "System overview and data statistics", "refreshData": "Refresh Data", "totalUsers": "Total Users", "todayNewUsers": "Today New Users: {count}", "totalQuestions": "Total Questions", "todayNewQuestions": "Today New Questions: {count}", "questionCategories": "Question Categories", "includeSubcategories": "Include Subcategories", "practiceRecords": "Practice Records", "todayPractices": "Today Practices: {count}", "accidentReports": "Accident Reports", "todayNewReports": "Today New Reports: {count}", "averageScore": "Average Score", "passRate": "Pass Rate: {rate}%", "userRegistrationTrend": "User Registration Trend", "last7DaysRegistration": "Last 7 Days Registration", "noData": "No Data", "practiceTrend": "Practice Trend", "last7DaysPractice": "Last 7 Days Practice", "quickActions": "Quick Actions", "commonManagementFunctions": "Common Management Functions", "questionManagement": "Question Management", "addEditDeleteQuestions": "Add, edit, delete questions", "userManagement": "User Management", "viewAndManageUsers": "View and manage users", "dataReports": "Data Reports", "viewDetailedStatistics": "View detailed statistics", "sendNotification": "Send Notification", "sendMessageToUsers": "Send message to users", "notificationTitle": "Title", "enterNotificationTitle": "Please enter notification title", "notificationContent": "Content", "enterNotificationContent": "Please enter notification content", "sendTarget": "Send Target", "allUsers": "All Users", "specificUser": "Specific User", "userId": "User ID", "enterUserId": "Please enter user ID", "send": "Send", "fetchStatsFailed": "Failed to fetch statistics", "notificationSentSuccess": "Notification sent successfully", "notificationSentFailed": "Failed to send notification"}, "messages": {"networkError": "Network error, please check your connection", "serverError": "Server error, please try again later", "unauthorized": "Unauthorized, please login again", "forbidden": "Insufficient permissions", "notFound": "Resource not found", "validationError": "Data validation failed", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "saveSuccess": "Saved successfully", "saveFailed": "Save failed", "deleteSuccess": "Deleted successfully", "deleteFailed": "Delete failed", "updateSuccess": "Updated successfully", "updateFailed": "Update failed", "error": "Operation failed", "success": "Operation successful", "warning": "Warning", "info": "Info"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Minimum {min} characters required", "maxLength": "Maximum {max} characters allowed", "numeric": "Please enter a number", "phone": "Please enter a valid phone number", "url": "Please enter a valid URL", "passwordStrength": "Password must be at least 8 characters, including uppercase, lowercase, and numbers", "pleaseEnter": "Please enter", "pleaseSelect": "Please select", "usernameRequired": "Please enter username", "emailRequired": "Please enter email", "currentPasswordRequired": "Please enter current password", "newPasswordRequired": "Please enter new password", "confirmPasswordRequired": "Please confirm new password", "invalidEmail": "Invalid email format", "passwordMinLength": "Password must be at least 6 characters", "passwordMismatch": "Passwords do not match", "usernameFormat": "Username can only contain letters, numbers, and underscores", "usernameLength": "Username must be between 3 and 50 characters"}, "accident": {"title": "Accident Records", "subtitle": "Record and manage traffic accident information, generate professional reports", "newReport": "New Report", "totalReports": "Total Reports", "draft": "Draft", "submitted": "Submitted", "archived": "Archived", "selectStatus": "Select Status", "dateTo": "To", "startDate": "Start Date", "endDate": "End Date", "clearFilters": "Clear Filters", "noReports": "No accident reports", "noReportsDesc": "Click the button above to create your first accident report", "noReportsFound": "No related reports found", "tryOtherFilters": "Please try other filter conditions", "createReport": "Create Report", "noLocation": "Location not specified", "noDescription": "No description", "photoCount": "{count} Photos", "createdAt": "Created At", "editReport": "Edit Accident Report", "accidentTime": "Accident Time", "selectAccidentTime": "Select accident time", "accidentLocation": "Accident Location", "enterAccidentLocation": "Please enter accident location", "accidentDescription": "Accident Description", "enterAccidentDescription": "Please describe the accident details...", "otherPartyInfo": "Other Party Information", "enterOtherPartyInfo": "Please enter information about the other vehicle/driver...", "scenePhotos": "Scene Photos", "uploadTip": "Supports JPG, PNG formats, max 10MB per image", "saveDraft": "Save Draft", "submitReport": "Submit Report", "updateReport": "Update Report", "reportDetailTitle": "Accident Report Details - {location}", "unknownLocation": "Unknown Location", "basicInfo": "Basic Information", "notSpecified": "Not Specified", "reportStatus": "Report Status", "downloadPDF": "Download PDF", "unknown": "Unknown", "pleaseSelectAccidentTime": "Please select accident time", "pleaseEnterAccidentLocation": "Please enter accident location", "fetchReportsFailed": "Failed to fetch accident reports", "fetchReportDetailFailed": "Failed to fetch report details"}, "schools": {"title": "Driving School Information", "subtitle": "Find nearby driving schools and choose the best one for you", "locating": "Locating...", "getLocation": "Get Location", "searchPlaceholder": "Search school name or address...", "filterType": "Filter Type", "partnerSchool": "Partner School", "regularSchool": "Regular School", "sortBy": "Sort By", "defaultSort": "<PERSON><PERSON><PERSON>", "nearestDistance": "Nearest Distance", "nameSort": "Name Sort", "clearFilters": "Clear Filters", "currentLocation": "Current Location", "locationObtained": "Location obtained", "searchNearby": "Search Nearby Schools", "noSchoolsFound": "No related schools found", "noSchoolsInfo": "No school information available", "tryOtherConditions": "Please try other search conditions", "tryLaterOrContact": "Please try again later or contact the administrator", "searchAgain": "Search Again", "addressNotProvided": "Address not provided", "makeCall": "Make Call", "navigate": "Navigate", "distanceFromYou": "Distance from you: {distance}", "basicInfo": "Basic Information", "address": "Address", "phone": "Phone", "website": "Website", "visitWebsite": "Visit Website", "notProvided": "Not Provided", "schoolIntroduction": "School Introduction", "locationInfo": "Location Information", "mapInDevelopment": "Map feature under development...", "coordinates": "Coordinates", "navigateToSchool": "Navigate to School", "meters": "meters", "kilometers": "kilometers", "geolocationNotSupported": "Your browser does not support geolocation", "latitude": "Latitude", "longitude": "Longitude", "locationSuccess": "Location obtained successfully", "locationFailed": "Failed to obtain location", "locationPermissionDenied": "Location permission denied", "locationUnavailable": "Location information unavailable", "locationTimeout": "Location request timed out", "pleaseGetLocationFirst": "Please obtain current location first", "foundNearbySchools": "Found {count} nearby schools", "searchNearbyFailed": "Failed to search nearby schools", "fetchSchoolsFailed": "Failed to fetch school list"}, "adminQuestions": {"title": "Question Management", "subtitle": "Manage system question bank, add, edit, and delete questions", "batchImport": "Batch Import", "addQuestion": "Add Question", "totalQuestions": "Total Questions", "singleChoice": "Single Choice", "trueFalse": "True/False", "searchPlaceholder": "Search question content...", "selectCategory": "Select Category", "selectType": "Select Type", "clearFilters": "Clear Filters", "noQuestionsFound": "No related questions found", "noQuestions": "No questions available", "tryOtherConditions": "Please try other search conditions", "addFirstQuestion": "Click the button above to add the first question", "questionList": "Question List", "totalCount": "Total {total} questions, current page {page}", "questionContent": "Question Content", "questionType": "Question Type", "category": "Category", "optionsCount": "Options Count", "creator": "Creator", "createdAt": "Created At", "actions": "Actions", "uncategorized": "Uncategorized", "unknown": "Unknown", "correctAnswer": "Correct Answer", "basicInfo": "Basic Information", "optionsList": "Options List", "answerExplanation": "Answer Explanation", "detailedExplanation": "Detailed Explanation", "timeInfo": "Time Information", "editQuestion": "Edit Question", "createQuestion": "Create Question", "updateQuestion": "Update Question", "image": "Image", "optionSetting": "Option Setting", "addOption": "Add Option"}, "nav": {"home": "Home", "practice": "Practice", "accident": "Accident", "bookmarks": "Bookmarks", "profile": "My Profile", "overview": "Overview", "questions": "Questions", "users": "Users", "reports": "Reports", "settings": "Settings", "admin": "Admin", "user": "User", "userCenter": "User Center", "practiceBank": "Practice Bank", "accidentRecord": "Accident Records", "bookmarkedQuestions": "Bookmarked Questions", "schoolInfo": "School Information", "personalProfile": "Personal Profile", "adminDashboard": "Admin Dashboard", "questionManagement": "Question Management", "userManagement": "User Management", "dataReports": "Data Reports", "systemSettings": "System Settings", "notificationManagement": "Notification Management", "languageSettings": "Language Settings", "logout": "Logout", "mainFeatures": "Main Features", "systemManagement": "System Management", "quickNavigation": "Quick Navigation", "commonFeatures": "Common Features", "recentVisits": "Recent Visits", "quickActions": "Quick Actions", "randomPractice": "Random Practice", "myBookmarks": "My Bookmarks", "learningProgress": "Learning Progress", "accidentReport": "Accident Report", "schools": "School Information", "notifications": "Notifications"}, "user": {"noEmail": "No email set", "neverLoggedIn": "Never logged in", "active": "Active", "disabled": "Disabled", "admin": "Administrator", "user": "User", "adminAccount": "Ad<PERSON> Account", "regularUser": "Regular User"}, "stats": {"totalUsers": "Total Users", "totalQuestions": "Total Questions", "practiceCount": "Practice Count", "accidentRecords": "Accident Records", "bookmarkedQuestions": "Bookmarked Questions", "averageScore": "Average Score"}, "pageTitle": {"home": "Home", "login": "<PERSON><PERSON>", "register": "Register", "userCenter": "User Center", "practice": "Practice", "startPractice": "Start Practice", "bookmarks": "Bookmarks", "profile": "Profile", "accidentReport": "Accident Report", "schools": "School Information", "adminDashboard": "Admin Dashboard", "questionManagement": "Question Management", "userManagement": "User Management", "reports": "Data Reports", "current": "Current Page", "languageTest": "Language Test", "notFound": "Page Not Found"}, "error": {"pageNotFound": "Page Not Found", "pageNotFoundDesc": "Sorry, the page you visited does not exist or has been deleted."}, "categories": {"allQuestions": "All Questions", "generalPractice": "General Practice"}, "adminUsers": {"title": "User Management", "subtitle": "Manage system users, view user details and statistics", "totalUsers": "Total Users", "activeUsers": "Active Users", "adminUsers": "Admins", "selectRole": "Select Role", "selectStatus": "Select Status", "noUsersFound": "No related users found", "noUsers": "No users", "noUsersDesc": "No users have registered in the system yet", "userList": "User List", "totalCount": "Total {total} users, current page {page}", "userInfo": "User Info", "role": "Role", "status": "Status", "language": "Language", "lastLogin": "Last Login", "createdAt": "Registered At", "updatedAt": "Updated At", "userDetail": "User Detail", "basicInfo": "Basic Info", "languagePreference": "Language Preference", "practiceStats": "Practice Stats", "practiceCount": "Practice Count", "averageScore": "Average Score", "bookmarkedQuestions": "Bookmarked Questions", "enableUser": "Enable User", "disableUser": "Disable User"}}